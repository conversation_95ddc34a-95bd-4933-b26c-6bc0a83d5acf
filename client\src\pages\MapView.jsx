import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';
import L from 'leaflet';
import { api } from '../api/client';
import { useSocket } from '../context/SocketContext';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom icons for different types
const createIcon = (color, symbol) => {
  return L.divIcon({
    className: 'custom-div-icon',
    html: `<div style="background-color: ${color}; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3); font-size: 14px;">${symbol}</div>`,
    iconSize: [30, 30],
    iconAnchor: [15, 15],
  });
};

const stationIcons = {
  FireStation: createIcon('#e74c3c', '🚒'),
  PoliceStation: createIcon('#3498db', '👮'),
  Hospital: createIcon('#2ecc71', '🏥'),
  Ambulance: createIcon('#f39c12', '🚑'),
  FireEngine: createIcon('#e74c3c', '🚒'),
  PatrolCar: createIcon('#3498db', '🚓'),
};

const incidentIcons = {
  Pending: createIcon('#f1c40f', '⚠️'),
  Verified: createIcon('#3498db', '✓'),
  'In-Action': createIcon('#e67e22', '🚨'),
  Resolved: createIcon('#2ecc71', '✅'),
};

const MapView = () => {
  const [stations, setStations] = useState([]);
  const [incidents, setIncidents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState('all');

  const { socket, joinIncidents, leaveIncidents } = useSocket();

  useEffect(() => {
    fetchData();
    if (socket) {
      joinIncidents();
      
      socket.on('incidents:new', (newIncident) => {
        setIncidents(prev => [newIncident, ...prev]);
      });

      socket.on('incidents:update', (updatedIncident) => {
        setIncidents(prev => 
          prev.map(incident => 
            incident._id === updatedIncident._id ? updatedIncident : incident
          )
        );
      });

      return () => {
        leaveIncidents();
        socket.off('incidents:new');
        socket.off('incidents:update');
      };
    }
  }, [socket, joinIncidents, leaveIncidents]);

  const fetchData = async () => {
    try {
      const [stationsRes, incidentsRes] = await Promise.all([
        api.get('/stations'),
        api.get('/reports'),
      ]);
      
      setStations(stationsRes.data.stations);
      setIncidents(incidentsRes.data.reports);
    } catch (err) {
      setError('Failed to fetch map data');
    } finally {
      setLoading(false);
    }
  };

  const filteredIncidents = incidents.filter(incident => {
    if (filter === 'all') return true;
    if (filter === 'active') return ['Pending', 'Verified', 'In-Action'].includes(incident.status);
    if (filter === 'critical') return incident.severity === 'Critical';
    if (filter === 'women-child') return ['Women Safety', 'Child Help'].includes(incident.type);
    return incident.status === filter;
  });

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="text-lg">Loading map...</div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Chennai Emergency Map</h1>
        <div className="flex gap-2">
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
          >
            <option value="all">All Incidents</option>
            <option value="active">Active Only</option>
            <option value="critical">Critical</option>
            <option value="women-child">Women & Child</option>
            <option value="Pending">Pending</option>
            <option value="Verified">Verified</option>
            <option value="In-Action">In Action</option>
            <option value="Resolved">Resolved</option>
          </select>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <div className="grid lg:grid-cols-4 gap-6">
        <div className="lg:col-span-3">
          <div className="card p-0 overflow-hidden">
            <MapContainer
              center={[13.0827, 80.2707]} // Chennai center
              zoom={12}
              style={{ height: '600px', width: '100%' }}
            >
              <TileLayer
                attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
              />
              
              {/* Stations */}
              {stations.map((station) => (
                <Marker
                  key={station._id}
                  position={[station.location.lat, station.location.lng]}
                  icon={stationIcons[station.type] || stationIcons.Hospital}
                >
                  <Popup>
                    <div>
                      <h3 className="font-semibold">{station.name}</h3>
                      <p className="text-sm text-gray-600">{station.type}</p>
                    </div>
                  </Popup>
                </Marker>
              ))}

              {/* Incidents */}
              {filteredIncidents.map((incident) => (
                <Marker
                  key={incident._id}
                  position={[incident.location.lat, incident.location.lng]}
                  icon={incidentIcons[incident.status] || incidentIcons.Pending}
                >
                  <Popup>
                    <div className="min-w-[200px]">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-semibold">{incident.type}</h3>
                        <span className={`px-2 py-1 rounded text-xs ${
                          incident.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                          incident.status === 'Verified' ? 'bg-blue-100 text-blue-800' :
                          incident.status === 'In-Action' ? 'bg-orange-100 text-orange-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {incident.status}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{incident.description}</p>
                      <div className="text-xs text-gray-500">
                        <p>Severity: {incident.severity}</p>
                        <p>Reported: {formatDate(incident.createdAt)}</p>
                        <p>ID: {incident._id.slice(-8)}</p>
                      </div>
                    </div>
                  </Popup>
                </Marker>
              ))}
            </MapContainer>
          </div>
        </div>

        <div className="space-y-4">
          <div className="card">
            <h3 className="font-semibold mb-3">Legend</h3>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-yellow-400 rounded-full"></div>
                <span>Pending Incidents</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                <span>Verified Incidents</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-orange-500 rounded-full"></div>
                <span>In-Action</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                <span>Resolved</span>
              </div>
              <hr className="my-2" />
              <div className="flex items-center gap-2">
                <span>🚒</span>
                <span>Fire Stations</span>
              </div>
              <div className="flex items-center gap-2">
                <span>👮</span>
                <span>Police Stations</span>
              </div>
              <div className="flex items-center gap-2">
                <span>🏥</span>
                <span>Hospitals</span>
              </div>
              <div className="flex items-center gap-2">
                <span>🚑</span>
                <span>Mobile Units</span>
              </div>
            </div>
          </div>

          <div className="card">
            <h3 className="font-semibold mb-3">Quick Stats</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Total Incidents:</span>
                <span className="font-semibold">{incidents.length}</span>
              </div>
              <div className="flex justify-between">
                <span>Active:</span>
                <span className="font-semibold text-orange-600">
                  {incidents.filter(i => ['Pending', 'Verified', 'In-Action'].includes(i.status)).length}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Critical:</span>
                <span className="font-semibold text-red-600">
                  {incidents.filter(i => i.severity === 'Critical').length}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Resolved:</span>
                <span className="font-semibold text-green-600">
                  {incidents.filter(i => i.status === 'Resolved').length}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MapView;
