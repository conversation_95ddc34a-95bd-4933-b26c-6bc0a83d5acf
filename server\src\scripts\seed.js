import dotenv from 'dotenv';
import bcrypt from 'bcryptjs';
import { connectDB } from '../config/db.js';
import Station from '../models/Station.js';
import User from '../models/User.js';

dotenv.config();

const stations = [
  { name: 'Chennai Central Fire Station', type: 'FireStation', location: { lat: 13.0827, lng: 80.2707 } },
  { name: 'Government General Hospital', type: 'Hospital', location: { lat: 13.0823, lng: 80.2785 } },
  { name: 'T. Nagar Police Station', type: 'PoliceStation', location: { lat: 13.0418, lng: 80.2340 } },
  { name: 'Adyar Cancer Institute', type: 'Hospital', location: { lat: 13.0067, lng: 80.2206 } },
  { name: 'Mylapore Police Station', type: 'PoliceStation', location: { lat: 13.0339, lng: 80.2619 } },
  { name: 'Mobile Ambulance A1', type: 'Ambulance', location: { lat: 13.0600, lng: 80.2500 } },
  { name: 'Fire Engine F3', type: 'FireEngine', location: { lat: 13.0700, lng: 80.2600 } },
  { name: 'Patrol Car P2', type: 'PatrolCar', location: { lat: 13.0500, lng: 80.2400 } },
  { name: 'Ambulance Unit B5', type: 'Ambulance', location: { lat: 13.0400, lng: 80.2300 } },
  { name: 'Fire Engine F7', type: 'FireEngine', location: { lat: 13.0900, lng: 80.2800 } },
];

const users = [
  {
    name: 'Admin User',
    email: '<EMAIL>',
    phone: '+919876543210',
    passwordHash: await bcrypt.hash('password', 10),
    role: 'admin',
  },
  {
    name: 'Demo Citizen',
    email: '<EMAIL>',
    phone: '+919876543211',
    passwordHash: await bcrypt.hash('password', 10),
    role: 'citizen',
  },
];

(async () => {
  try {
    await connectDB();

    // Clear existing data
    await Station.deleteMany({});
    await User.deleteMany({});

    // Insert new data
    await Station.insertMany(stations);
    await User.insertMany(users);

    // eslint-disable-next-line no-console
    console.log('Seeded stations and demo users');
    console.log('Demo credentials:');
    console.log('Admin: <EMAIL> / password');
    console.log('Citizen: <EMAIL> / password');

    process.exit(0);
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error(e);
    process.exit(1);
  }
})();

