import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const Navbar = () => {
  const { user, logout, isAuthenticated, isAdmin } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  return (
    <nav className="bg-primary text-white shadow-lg">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          <Link to="/" className="text-xl font-bold">
            🌍 UrbanGuardians
          </Link>

          <div className="flex items-center space-x-4">
            {isAuthenticated ? (
              <>
                <Link to="/map" className="hover:text-gray-300">
                  Map
                </Link>
                {!isAdmin && (
                  <>
                    <Link to="/report" className="hover:text-gray-300">
                      Report
                    </Link>
                    <Link to="/my-reports" className="hover:text-gray-300">
                      My Reports
                    </Link>
                  </>
                )}
                {isAdmin && (
                  <>
                    <Link to="/admin" className="hover:text-gray-300">
                      Dashboard
                    </Link>
                    <Link to="/admin/women-child" className="hover:text-gray-300">
                      Women & Child
                    </Link>
                  </>
                )}
                <div className="flex items-center space-x-2">
                  <span className="text-sm">Hi, {user?.name}</span>
                  <button
                    onClick={handleLogout}
                    className="bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-sm"
                  >
                    Logout
                  </button>
                </div>
              </>
            ) : (
              <>
                <Link to="/login" className="hover:text-gray-300">
                  Login
                </Link>
                <Link
                  to="/register"
                  className="bg-secondary hover:bg-secondary/90 px-4 py-2 rounded"
                >
                  Register
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
