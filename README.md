# 🌍 UrbanGuardians MVP

A MERN stack web application for citizen disaster & safety reporting with real-time authority response dashboard.

## ✨ Features

### Citizen Interface
- **Report Incidents**: Text + image upload, auto-geolocation, category selection
- **Real-time Updates**: Live status tracking via WebSockets
- **Multi-channel Alerts**: SMS/WhatsApp notifications (Twilio integration)
- **Map View**: Chennai-centered map with incidents and emergency services
- **Chat Support**: Real-time chat with authorities

### Admin Dashboard
- **Incident Management**: Real-time table with status updates
- **One-click Actions**: Dispatch fire/police, notify hospitals (n8n-ready)
- **Damage Tracking**: People affected, houses damaged, infrastructure
- **Women & Child Safety**: Priority tab for sensitive cases
- **Credibility Scoring**: AI-ready incident verification

### n8n Integration Ready
All workflows exposed as REST endpoints:
- `/api/workflows/notify-hospital`
- `/api/workflows/dispatch-police`
- `/api/workflows/dispatch-fire`
- `/api/workflows/credibility-check`
- `/api/workflows/alert-citizen`

## 🛠️ Tech Stack

- **Frontend**: React + Vite + TailwindCSS + Leaflet
- **Backend**: Node.js + Express + Socket.IO
- **Database**: MongoDB + Mongoose
- **Auth**: JWT + bcrypt
- **Maps**: OpenStreetMap + react-leaflet
- **Messaging**: Twilio (SMS + WhatsApp)
- **Real-time**: Socket.IO

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- MongoDB (local or Atlas)
- Git

### Installation

1. **Clone and setup**
   ```bash
   git clone <repo>
   cd UrbanGuardians
   ```

2. **Backend setup**
   ```bash
   cd server
   npm install
   cp .env.example .env
   # Edit .env with your MongoDB URI and JWT secret
   npm run seed  # Seed demo data
   npm run dev   # Start server on port 5000
   ```

3. **Frontend setup**
   ```bash
   cd client
   npm install
   npm run dev   # Start client on port 5173
   ```

### Demo Credentials
- **Admin**: <EMAIL> / password
- **Citizen**: <EMAIL> / password

## 📱 Usage

1. **Citizen Flow**:
   - Register/Login → Report Incident → Track Status → Chat with Support

2. **Admin Flow**:
   - Login → View Dashboard → Update Status → Trigger Workflows → Track Damage

3. **Map View**:
   - Real-time incident markers (color-coded by status)
   - Emergency services locations
   - Live updates via WebSockets

## 🎨 Design

**Color Theme**:
- Primary: Deep Blue (#003366) - trust & authority
- Secondary: Safety Green (#2ecc71) - hope & safety  
- Alert: Red (#e74c3c) - emergencies
- Pending: Yellow (#f1c40f) - pending status

**UI Style**: Clean, card-based, WhatsApp + Google Maps inspired

## 🔧 Configuration

### Environment Variables

**Server (.env)**:
```
PORT=5000
CLIENT_URL=http://localhost:5173
MONGODB_URI=mongodb://127.0.0.1:27017/urbanguardians
JWT_SECRET=your_jwt_secret

# Optional Twilio
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_PHONE_NUMBER=
TWILIO_WHATSAPP_NUMBER=
```

**Client (.env)**:
```
VITE_API_URL=http://localhost:5000/api
```

## 📊 API Endpoints

### Auth
- `POST /api/auth/register`
- `POST /api/auth/login`
- `GET /api/auth/me`

### Reports
- `POST /api/reports` (with file upload)
- `GET /api/reports`
- `GET /api/reports/:id`
- `PATCH /api/reports/:id/status`

### Workflows (n8n-ready)
- `POST /api/workflows/notify-hospital`
- `POST /api/workflows/dispatch-police`
- `POST /api/workflows/dispatch-fire`
- `POST /api/workflows/credibility-check`
- `POST /api/workflows/alert-citizen`

### Real-time Events
- `incidents:new` - New incident reported
- `incidents:update` - Incident status changed
- `chat:new_message` - New chat message

## 🗺️ Map Features

- **Center**: Chennai (13.0827, 80.2707)
- **Markers**: 
  - 🚒 Fire stations/engines
  - 👮 Police stations/patrol cars
  - 🏥 Hospitals/ambulances
  - ⚠️ Incidents (color by status)

## 🔮 Future Enhancements

- [ ] n8n workflow integration
- [ ] AI credibility scoring
- [ ] IoT sensor integration
- [ ] Mobile app (React Native)
- [ ] Advanced analytics dashboard
- [ ] Multi-language support

## 📄 License

MIT License

---

**Built with ❤️ for safer cities**
