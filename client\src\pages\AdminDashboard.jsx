import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { api } from '../api/client';
import { useAuth } from '../context/AuthContext';
import { useSocket } from '../context/SocketContext';

const AdminDashboard = () => {
  const [incidents, setIncidents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState('all');
  const [selectedIncident, setSelectedIncident] = useState(null);
  const [damageModal, setDamageModal] = useState(null);
  const [damageData, setDamageData] = useState({
    peopleAffected: 0,
    housesDamaged: 0,
    infrastructureHit: '',
  });

  const { isAuthenticated, isAdmin } = useAuth();
  const { socket, joinIncidents, leaveIncidents } = useSocket();
  const navigate = useNavigate();

  useEffect(() => {
    if (!isAuthenticated || !isAdmin) {
      navigate('/login');
      return;
    }
    fetchIncidents();
    
    if (socket) {
      joinIncidents();
      
      socket.on('incidents:new', (newIncident) => {
        setIncidents(prev => [newIncident, ...prev]);
      });

      socket.on('incidents:update', (updatedIncident) => {
        setIncidents(prev => 
          prev.map(incident => 
            incident._id === updatedIncident._id ? updatedIncident : incident
          )
        );
      });

      return () => {
        leaveIncidents();
        socket.off('incidents:new');
        socket.off('incidents:update');
      };
    }
  }, [isAuthenticated, isAdmin, navigate, socket, joinIncidents, leaveIncidents]);

  const fetchIncidents = async () => {
    try {
      const response = await api.get('/reports');
      setIncidents(response.data.reports);
    } catch (err) {
      setError('Failed to fetch incidents');
    } finally {
      setLoading(false);
    }
  };

  const updateStatus = async (incidentId, newStatus) => {
    try {
      await api.patch(`/reports/${incidentId}/status`, { status: newStatus });
      // The socket will handle the real-time update
    } catch (err) {
      setError('Failed to update status');
    }
  };

  const triggerWorkflow = async (workflow, incidentId) => {
    try {
      await api.post(`/workflows/${workflow}`, { incidentId });
      alert(`${workflow} workflow triggered successfully!`);
    } catch (err) {
      setError(`Failed to trigger ${workflow}`);
    }
  };

  const updateDamage = async () => {
    try {
      await api.patch(`/reports/${damageModal}`, { damage: damageData });
      setDamageModal(null);
      fetchIncidents(); // Refresh data
    } catch (err) {
      setError('Failed to update damage data');
    }
  };

  const filteredIncidents = incidents.filter(incident => {
    if (filter === 'all') return true;
    if (filter === 'active') return ['Pending', 'Verified', 'In-Action'].includes(incident.status);
    if (filter === 'critical') return incident.severity === 'Critical';
    return incident.status === filter;
  });

  const getStatusColor = (status) => {
    switch (status) {
      case 'Pending': return 'bg-yellow-100 text-yellow-800';
      case 'Verified': return 'bg-blue-100 text-blue-800';
      case 'In-Action': return 'bg-orange-100 text-orange-800';
      case 'Resolved': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'Low': return 'bg-green-100 text-green-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  if (!isAuthenticated || !isAdmin) {
    return null;
  }

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="text-lg">Loading dashboard...</div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Admin Dashboard</h1>
        <div className="flex gap-2">
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
          >
            <option value="all">All Incidents</option>
            <option value="active">Active Only</option>
            <option value="critical">Critical</option>
            <option value="Pending">Pending</option>
            <option value="Verified">Verified</option>
            <option value="In-Action">In Action</option>
            <option value="Resolved">Resolved</option>
          </select>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid md:grid-cols-4 gap-4 mb-6">
        <div className="card">
          <h3 className="text-sm font-medium text-gray-500">Total Incidents</h3>
          <p className="text-2xl font-bold">{incidents.length}</p>
        </div>
        <div className="card">
          <h3 className="text-sm font-medium text-gray-500">Active</h3>
          <p className="text-2xl font-bold text-orange-600">
            {incidents.filter(i => ['Pending', 'Verified', 'In-Action'].includes(i.status)).length}
          </p>
        </div>
        <div className="card">
          <h3 className="text-sm font-medium text-gray-500">Critical</h3>
          <p className="text-2xl font-bold text-red-600">
            {incidents.filter(i => i.severity === 'Critical').length}
          </p>
        </div>
        <div className="card">
          <h3 className="text-sm font-medium text-gray-500">Resolved</h3>
          <p className="text-2xl font-bold text-green-600">
            {incidents.filter(i => i.status === 'Resolved').length}
          </p>
        </div>
      </div>

      {/* Incidents Table */}
      <div className="card overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Incident
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Location
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Severity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Credibility
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredIncidents.map((incident) => (
                <tr key={incident._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{incident.type}</div>
                      <div className="text-sm text-gray-500">{incident.description.slice(0, 50)}...</div>
                      <div className="text-xs text-gray-400">{formatDate(incident.createdAt)}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    📍 {incident.location.lat.toFixed(4)}, {incident.location.lng.toFixed(4)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <select
                      value={incident.status}
                      onChange={(e) => updateStatus(incident._id, e.target.value)}
                      className={`px-2 py-1 rounded-full text-sm border-0 ${getStatusColor(incident.status)}`}
                    >
                      <option value="Pending">Pending</option>
                      <option value="Verified">Verified</option>
                      <option value="In-Action">In-Action</option>
                      <option value="Resolved">Resolved</option>
                    </select>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 rounded-full text-sm ${getSeverityColor(incident.severity)}`}>
                      {incident.severity}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {(incident.credibilityScore * 100).toFixed(0)}%
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                    <button
                      onClick={() => triggerWorkflow('dispatch-fire', incident._id)}
                      className="text-red-600 hover:text-red-900"
                      title="Dispatch Fire"
                    >
                      🚒
                    </button>
                    <button
                      onClick={() => triggerWorkflow('dispatch-police', incident._id)}
                      className="text-blue-600 hover:text-blue-900"
                      title="Dispatch Police"
                    >
                      👮
                    </button>
                    <button
                      onClick={() => triggerWorkflow('notify-hospital', incident._id)}
                      className="text-green-600 hover:text-green-900"
                      title="Notify Hospital"
                    >
                      🏥
                    </button>
                    <button
                      onClick={() => setDamageModal(incident._id)}
                      className="text-purple-600 hover:text-purple-900"
                      title="Track Damage"
                    >
                      📊
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Damage Tracking Modal */}
      {damageModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Track Damage</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  People Affected
                </label>
                <input
                  type="number"
                  value={damageData.peopleAffected}
                  onChange={(e) => setDamageData({...damageData, peopleAffected: parseInt(e.target.value) || 0})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Houses Damaged
                </label>
                <input
                  type="number"
                  value={damageData.housesDamaged}
                  onChange={(e) => setDamageData({...damageData, housesDamaged: parseInt(e.target.value) || 0})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Infrastructure Hit
                </label>
                <textarea
                  value={damageData.infrastructureHit}
                  onChange={(e) => setDamageData({...damageData, infrastructureHit: e.target.value})}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
            </div>
            <div className="flex gap-2 mt-6">
              <button
                onClick={updateDamage}
                className="btn-primary"
              >
                Save
              </button>
              <button
                onClick={() => setDamageModal(null)}
                className="btn-secondary"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;
