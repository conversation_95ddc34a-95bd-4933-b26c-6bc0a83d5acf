import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { api } from '../api/client';
import { useAuth } from '../context/AuthContext';
import { useSocket } from '../context/SocketContext';

const AdminWomenChild = () => {
  const [incidents, setIncidents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const { isAuthenticated, isAdmin } = useAuth();
  const { socket, joinIncidents, leaveIncidents } = useSocket();
  const navigate = useNavigate();

  useEffect(() => {
    if (!isAuthenticated || !isAdmin) {
      navigate('/login');
      return;
    }
    fetchIncidents();
    
    if (socket) {
      joinIncidents();
      
      socket.on('incidents:new', (newIncident) => {
        if (['Women Safety', 'Child Help'].includes(newIncident.type)) {
          setIncidents(prev => [newIncident, ...prev]);
        }
      });

      socket.on('incidents:update', (updatedIncident) => {
        if (['Women Safety', 'Child Help'].includes(updatedIncident.type)) {
          setIncidents(prev => 
            prev.map(incident => 
              incident._id === updatedIncident._id ? updatedIncident : incident
            )
          );
        }
      });

      return () => {
        leaveIncidents();
        socket.off('incidents:new');
        socket.off('incidents:update');
      };
    }
  }, [isAuthenticated, isAdmin, navigate, socket, joinIncidents, leaveIncidents]);

  const fetchIncidents = async () => {
    try {
      const response = await api.get('/reports?type=Women Safety,Child Help');
      // Filter on client side since backend might not support comma-separated types
      const filtered = response.data.reports.filter(report => 
        ['Women Safety', 'Child Help'].includes(report.type)
      );
      setIncidents(filtered);
    } catch (err) {
      setError('Failed to fetch incidents');
    } finally {
      setLoading(false);
    }
  };

  const updateStatus = async (incidentId, newStatus) => {
    try {
      await api.patch(`/reports/${incidentId}/status`, { status: newStatus });
    } catch (err) {
      setError('Failed to update status');
    }
  };

  const triggerWorkflow = async (workflow, incidentId) => {
    try {
      await api.post(`/workflows/${workflow}`, { incidentId });
      alert(`${workflow} workflow triggered successfully!`);
    } catch (err) {
      setError(`Failed to trigger ${workflow}`);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Pending': return 'bg-yellow-100 text-yellow-800';
      case 'Verified': return 'bg-blue-100 text-blue-800';
      case 'In-Action': return 'bg-orange-100 text-orange-800';
      case 'Resolved': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'Low': return 'bg-green-100 text-green-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  if (!isAuthenticated || !isAdmin) {
    return null;
  }

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="text-lg">Loading women & child safety reports...</div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Women & Child Safety</h1>
          <p className="text-gray-600">Priority safety reports requiring immediate attention</p>
        </div>
        <div className="text-right">
          <div className="text-sm text-gray-500">Total Reports</div>
          <div className="text-2xl font-bold text-red-600">{incidents.length}</div>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Priority Alert */}
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div className="flex items-center">
          <div className="text-red-600 text-xl mr-3">🚨</div>
          <div>
            <h3 className="text-red-800 font-semibold">High Priority Cases</h3>
            <p className="text-red-700 text-sm">
              These reports require immediate attention and coordination with specialized units.
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid md:grid-cols-4 gap-4 mb-6">
        <div className="card">
          <h3 className="text-sm font-medium text-gray-500">Women Safety</h3>
          <p className="text-2xl font-bold text-purple-600">
            {incidents.filter(i => i.type === 'Women Safety').length}
          </p>
        </div>
        <div className="card">
          <h3 className="text-sm font-medium text-gray-500">Child Help</h3>
          <p className="text-2xl font-bold text-blue-600">
            {incidents.filter(i => i.type === 'Child Help').length}
          </p>
        </div>
        <div className="card">
          <h3 className="text-sm font-medium text-gray-500">Active Cases</h3>
          <p className="text-2xl font-bold text-orange-600">
            {incidents.filter(i => ['Pending', 'Verified', 'In-Action'].includes(i.status)).length}
          </p>
        </div>
        <div className="card">
          <h3 className="text-sm font-medium text-gray-500">Critical</h3>
          <p className="text-2xl font-bold text-red-600">
            {incidents.filter(i => i.severity === 'Critical').length}
          </p>
        </div>
      </div>

      {incidents.length === 0 ? (
        <div className="card text-center">
          <div className="text-4xl mb-4">🛡️</div>
          <h2 className="text-xl font-semibold mb-2">No reports yet</h2>
          <p className="text-gray-600">No women & child safety reports have been submitted.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {incidents.map((incident) => (
            <div key={incident._id} className="card border-l-4 border-l-red-500">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="text-2xl">
                      {incident.type === 'Women Safety' ? '👩' : '👶'}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold">{incident.type}</h3>
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded-full text-sm ${getSeverityColor(incident.severity)}`}>
                          {incident.severity}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-sm ${getStatusColor(incident.status)}`}>
                          {incident.status}
                        </span>
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-3">{incident.description}</p>
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <span>📍 {incident.location.lat.toFixed(4)}, {incident.location.lng.toFixed(4)}</span>
                    <span>🕒 {formatDate(incident.createdAt)}</span>
                    <span>ID: {incident._id.slice(-8)}</span>
                    <span>Credibility: {(incident.credibilityScore * 100).toFixed(0)}%</span>
                  </div>
                </div>
              </div>

              <div className="border-t pt-4">
                <div className="flex justify-between items-center">
                  <div className="flex gap-2">
                    <select
                      value={incident.status}
                      onChange={(e) => updateStatus(incident._id, e.target.value)}
                      className={`px-3 py-1 rounded border ${getStatusColor(incident.status)}`}
                    >
                      <option value="Pending">Pending</option>
                      <option value="Verified">Verified</option>
                      <option value="In-Action">In-Action</option>
                      <option value="Resolved">Resolved</option>
                    </select>
                  </div>
                  
                  <div className="flex gap-2">
                    <button
                      onClick={() => triggerWorkflow('notify-control-room', incident._id)}
                      className="btn-alert text-sm"
                    >
                      🚨 Notify Control Room
                    </button>
                    <button
                      onClick={() => triggerWorkflow('dispatch-police', incident._id)}
                      className="btn-primary text-sm"
                    >
                      👮 Dispatch Police
                    </button>
                    <button
                      onClick={() => {/* TODO: Mark safe house */}}
                      className="btn-secondary text-sm"
                    >
                      🏠 Mark Safe House
                    </button>
                  </div>
                </div>
              </div>

              {incident.mediaUrl && (
                <div className="mt-4 pt-4 border-t">
                  <p className="text-sm text-gray-600 mb-2">Attached Evidence:</p>
                  <a 
                    href={`http://localhost:5000${incident.mediaUrl}`} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-primary hover:underline text-sm"
                  >
                    📎 View Image
                  </a>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AdminWomenChild;
