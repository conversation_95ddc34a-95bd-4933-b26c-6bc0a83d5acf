import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const Home = () => {
  const { isAuthenticated, isAdmin } = useAuth();

  return (
    <div className="max-w-4xl mx-auto text-center">
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-primary mb-4">
          🌍 UrbanGuardians
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          Citizen disaster & safety reporting platform with real-time authority response
        </p>
      </div>

      {!isAuthenticated ? (
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          <div className="card">
            <h2 className="text-2xl font-semibold mb-4">For Citizens</h2>
            <p className="text-gray-600 mb-6">
              Report incidents, track status, and stay connected with emergency services
            </p>
            <Link to="/register" className="btn-primary">
              Get Started
            </Link>
          </div>
          <div className="card">
            <h2 className="text-2xl font-semibold mb-4">For Authorities</h2>
            <p className="text-gray-600 mb-6">
              Manage incidents, coordinate response, and track damage assessments
            </p>
            <Link to="/login" className="btn-secondary">
              Admin Login
            </Link>
          </div>
        </div>
      ) : (
        <div className="grid md:grid-cols-3 gap-6 mb-12">
          {!isAdmin ? (
            <>
              <Link to="/report" className="card hover:shadow-lg transition-shadow">
                <div className="text-4xl mb-4">🚨</div>
                <h3 className="text-xl font-semibold mb-2">Report Incident</h3>
                <p className="text-gray-600">Report emergencies and safety concerns</p>
              </Link>
              <Link to="/my-reports" className="card hover:shadow-lg transition-shadow">
                <div className="text-4xl mb-4">📋</div>
                <h3 className="text-xl font-semibold mb-2">My Reports</h3>
                <p className="text-gray-600">Track your submitted reports</p>
              </Link>
              <Link to="/map" className="card hover:shadow-lg transition-shadow">
                <div className="text-4xl mb-4">🗺️</div>
                <h3 className="text-xl font-semibold mb-2">Map View</h3>
                <p className="text-gray-600">View incidents and emergency services</p>
              </Link>
            </>
          ) : (
            <>
              <Link to="/admin" className="card hover:shadow-lg transition-shadow">
                <div className="text-4xl mb-4">📊</div>
                <h3 className="text-xl font-semibold mb-2">Dashboard</h3>
                <p className="text-gray-600">Manage all incidents and responses</p>
              </Link>
              <Link to="/admin/women-child" className="card hover:shadow-lg transition-shadow">
                <div className="text-4xl mb-4">🛡️</div>
                <h3 className="text-xl font-semibold mb-2">Women & Child</h3>
                <p className="text-gray-600">Priority safety reports</p>
              </Link>
              <Link to="/map" className="card hover:shadow-lg transition-shadow">
                <div className="text-4xl mb-4">🗺️</div>
                <h3 className="text-xl font-semibold mb-2">Map View</h3>
                <p className="text-gray-600">Real-time incident overview</p>
              </Link>
            </>
          )}
        </div>
      )}

      <div className="bg-white rounded-lg shadow-md p-8">
        <h2 className="text-2xl font-semibold mb-6">Key Features</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl mb-2">⚡</div>
            <h3 className="font-semibold">Real-time Updates</h3>
            <p className="text-sm text-gray-600">Live incident tracking</p>
          </div>
          <div className="text-center">
            <div className="text-3xl mb-2">📱</div>
            <h3 className="font-semibold">Multi-channel</h3>
            <p className="text-sm text-gray-600">SMS & WhatsApp alerts</p>
          </div>
          <div className="text-center">
            <div className="text-3xl mb-2">🎯</div>
            <h3 className="font-semibold">Geo-location</h3>
            <p className="text-sm text-gray-600">Precise incident mapping</p>
          </div>
          <div className="text-center">
            <div className="text-3xl mb-2">🤖</div>
            <h3 className="font-semibold">AI-Ready</h3>
            <p className="text-sm text-gray-600">n8n workflow integration</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
