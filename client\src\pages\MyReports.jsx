import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { api } from '../api/client';
import { useAuth } from '../context/AuthContext';
import { useSocket } from '../context/SocketContext';
import Chat from '../components/Chat';

const MyReports = () => {
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedReport, setSelectedReport] = useState(null);
  const [chatReport, setChatReport] = useState(null);

  const { isAuthenticated } = useAuth();
  const { socket } = useSocket();
  const navigate = useNavigate();

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }
    fetchReports();
  }, [isAuthenticated, navigate]);

  useEffect(() => {
    if (socket) {
      socket.on('incidents:update', (updatedReport) => {
        setReports(prev => 
          prev.map(report => 
            report._id === updatedReport._id ? updatedReport : report
          )
        );
      });

      return () => {
        socket.off('incidents:update');
      };
    }
  }, [socket]);

  const fetchReports = async () => {
    try {
      const response = await api.get('/users/me/reports');
      setReports(response.data.reports);
    } catch (err) {
      setError('Failed to fetch reports');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Pending': return 'status-pending';
      case 'Verified': return 'status-verified';
      case 'In-Action': return 'status-in-action';
      case 'Resolved': return 'status-resolved';
      default: return 'status-pending';
    }
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'Low': return 'bg-green-100 text-green-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  if (!isAuthenticated) {
    return null;
  }

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="text-lg">Loading your reports...</div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">My Reports</h1>
        <button
          onClick={() => navigate('/report')}
          className="btn-primary"
        >
          + New Report
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {reports.length === 0 ? (
        <div className="card text-center">
          <div className="text-4xl mb-4">📋</div>
          <h2 className="text-xl font-semibold mb-2">No reports yet</h2>
          <p className="text-gray-600 mb-4">You haven't submitted any incident reports.</p>
          <button
            onClick={() => navigate('/report')}
            className="btn-primary"
          >
            Submit Your First Report
          </button>
        </div>
      ) : (
        <div className="grid gap-4">
          {reports.map((report) => (
            <div key={report._id} className="card">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="text-lg font-semibold">{report.type}</h3>
                    <span className={`px-2 py-1 rounded-full text-sm ${getSeverityColor(report.severity)}`}>
                      {report.severity}
                    </span>
                  </div>
                  <p className="text-gray-600 mb-2">{report.description}</p>
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <span>📍 {report.location.lat.toFixed(4)}, {report.location.lng.toFixed(4)}</span>
                    <span>🕒 {formatDate(report.createdAt)}</span>
                    <span>ID: {report._id.slice(-8)}</span>
                  </div>
                </div>
                <div className="flex flex-col items-end gap-2">
                  <span className={getStatusColor(report.status)}>
                    {report.status}
                  </span>
                  <button
                    onClick={() => setSelectedReport(selectedReport === report._id ? null : report._id)}
                    className="text-sm text-primary hover:underline"
                  >
                    {selectedReport === report._id ? 'Hide Details' : 'View Details'}
                  </button>
                </div>
              </div>

              {selectedReport === report._id && (
                <div className="border-t pt-4 mt-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold mb-2">Report Details</h4>
                      <div className="space-y-1 text-sm">
                        <p><strong>Credibility Score:</strong> {(report.credibilityScore * 100).toFixed(0)}%</p>
                        <p><strong>Created:</strong> {formatDate(report.createdAt)}</p>
                        <p><strong>Last Updated:</strong> {formatDate(report.updatedAt)}</p>
                        {report.mediaUrl && (
                          <p>
                            <strong>Media:</strong>{' '}
                            <a 
                              href={`http://localhost:5000${report.mediaUrl}`} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-primary hover:underline"
                            >
                              View Image
                            </a>
                          </p>
                        )}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Status Timeline</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-pending rounded-full"></div>
                          <span>Pending</span>
                        </div>
                        {report.status !== 'Pending' && (
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <span>Verified</span>
                          </div>
                        )}
                        {(report.status === 'In-Action' || report.status === 'Resolved') && (
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                            <span>In Action</span>
                          </div>
                        )}
                        {report.status === 'Resolved' && (
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-secondary rounded-full"></div>
                            <span>Resolved</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-4 pt-4 border-t">
                    <button
                      onClick={() => setChatReport(report._id)}
                      className="btn-secondary text-sm"
                    >
                      💬 Chat with Support
                    </button>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Chat Modal */}
      {chatReport && (
        <Chat
          reportId={chatReport}
          onClose={() => setChatReport(null)}
        />
      )}
    </div>
  );
};

export default MyReports;
